{"name": "stationery-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "init-db": "node mongodb/init-database.js"}, "dependencies": {"bcryptjs": "^3.0.2", "clsx": "^2.1.0", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "gh-pages": "^6.3.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "mongodb": "^6.18.0", "razorpay": "^2.9.6", "react": "^18.3.1", "react-datepicker": "^6.1.0", "react-dom": "^18.3.1", "react-router-dom": "^6.22.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.4.2"}}