# Stationery Management System

A modern, full-featured stationery management system built with React and MongoDB. This application allows customers to browse and order stationery items while providing administrators with comprehensive management tools.

## 🚀 Features

### Customer Features
- **Browse Products**: View available stationery items with real-time stock levels
- **Shopping Cart**: Add/remove items with quantity management
- **Booking System**: Select date and time slots for pickup
- **Payment Integration**: Razorpay integration for online payments
- **Order Tracking**: Check order status with order numbers
- **Rush Hour Indicators**: Visual indicators for busy time slots

### Admin Features
- **Dashboard**: Comprehensive admin panel with analytics
- **Order Management**: View, search, and manage all orders
- **Stock Management**: Real-time inventory tracking and updates
- **Rush Status**: Manage busy time slots and capacity
- **Revenue Tracking**: Monitor sales and business metrics

## 🛠️ Technology Stack

- **Frontend**: React 18, Vite, Tailwind CSS
- **Database**: MongoDB
- **Authentication**: JWT-based authentication
- **Payment**: Razorpay integration
- **Icons**: Lucide React
- **Date Handling**: date-fns

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn package manager

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd stationery-management-system
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env.local` file in the root directory:

```env
# MongoDB Configuration
VITE_MONGODB_URI=mongodb://localhost:27017/stationery_management
VITE_JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Razorpay Configuration (for payments)
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
```

### 4. Database Setup

#### Option A: Local MongoDB
1. Install MongoDB Community Server
2. Start MongoDB service
3. Initialize the database:
```bash
npm run init-db
```

#### Option B: MongoDB Atlas (Cloud)
1. Create a MongoDB Atlas account
2. Create a cluster and get the connection string
3. Update `VITE_MONGODB_URI` with your Atlas connection string
4. Initialize the database:
```bash
npm run init-db
```

### 5. Start the Application
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## 🔐 Default Admin Credentials

- **Email**: `<EMAIL>`
- **Password**: `admin123`

> **Note**: Change these credentials in production by updating the authentication logic in `src/lib/mongodb.js`

## 📚 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run init-db` - Initialize MongoDB database

## 📖 Documentation

- **[MongoDB Setup Guide](MONGODB_SETUP.md)** - Detailed database setup instructions
- **[Migration Guide](MONGODB_MIGRATION_COMPLETE.md)** - Complete migration documentation

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts (Auth, etc.)
├── lib/               # Database and external service configs
├── pages/             # Main application pages
├── utils/             # Utility functions
└── App.jsx            # Main application component

mongodb/
└── init-database.js   # Database initialization script
```

## 🔄 Database Schema

### Collections
- **stationery_items**: Product catalog with stock levels
- **bookings**: Customer orders and booking details
- **rush_status**: Time slot capacity management
- **counters**: Auto-incrementing order numbers

## 🎯 Usage

### For Customers
1. Visit the homepage
2. Browse available stationery items
3. Add items to cart
4. Enter customer details
5. Select pickup date and time
6. Choose payment method
7. Complete the booking

### For Administrators
1. Go to `/admin/login`
2. Login with admin credentials
3. Access the admin dashboard
4. Manage orders, stock, and rush status

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Environment Variables for Production
Ensure all environment variables are properly set:
- Use a secure JWT secret
- Configure production MongoDB instance
- Set up production Razorpay keys

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For setup help or issues:
1. Check the MongoDB Setup Guide
2. Verify environment variables
3. Ensure MongoDB is running
4. Check browser console for errors

## 🎉 Acknowledgments

- Built with React and modern web technologies
- MongoDB for flexible data storage
- Razorpay for payment processing
- Tailwind CSS for styling
