# MongoDB Setup Instructions

## Quick Setup (10 minutes)

### Option A: Local MongoDB Setup

#### Step 1: Install MongoDB
1. **Windows**: Download MongoDB Community Server from [mongodb.com](https://www.mongodb.com/try/download/community)
2. **macOS**: Use Homebrew: `brew install mongodb-community`
3. **Linux**: Follow the [official installation guide](https://docs.mongodb.com/manual/administration/install-on-linux/)

#### Step 2: Start MongoDB Service
```bash
# Windows (as Administrator)
net start MongoDB

# macOS/Linux
brew services start mongodb-community
# or
sudo systemctl start mongod
```

#### Step 3: Configure Environment Variables
Create or update your `.env.local` file:
```env
# MongoDB Configuration
VITE_MONGODB_URI=mongodb://localhost:27017/stationery_management
VITE_JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

#### Step 4: Initialize Database
```bash
npm run init-db
```

### Option B: MongoDB Atlas (Cloud) Setup

#### Step 1: Create MongoDB Atlas Account
1. Go to [mongodb.com/atlas](https://www.mongodb.com/atlas)
2. Sign up for a free account
3. Create a new cluster (free tier available)

#### Step 2: Configure Database Access
1. In Atlas dashboard, go to **Database Access**
2. Click **Add New Database User**
3. Create a user with read/write permissions
4. Note down the username and password

#### Step 3: Configure Network Access
1. Go to **Network Access**
2. Click **Add IP Address**
3. Add your current IP or `0.0.0.0/0` for development (not recommended for production)

#### Step 4: Get Connection String
1. Go to **Clusters** and click **Connect**
2. Choose **Connect your application**
3. Copy the connection string
4. Replace `<password>` with your database user password

#### Step 5: Configure Environment Variables
Update your `.env.local` file:
```env
# MongoDB Atlas Configuration
VITE_MONGODB_URI=mongodb+srv://username:<EMAIL>/stationery_management?retryWrites=true&w=majority
VITE_JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

#### Step 6: Initialize Database
```bash
npm run init-db
```

## Database Schema

### Collections Created

#### stationery_items
```javascript
{
  _id: ObjectId,
  name: String,
  price: Number,
  stock_quantity: Number,
  created_at: Date
}
```

#### bookings
```javascript
{
  _id: ObjectId,
  customer_name: String,
  date: String, // ISO date string
  time_slot: String,
  items: Array, // Array of item objects
  total_cost: Number,
  order_number: String,
  order_status: String,
  payment_method: String,
  payment_status: String,
  payment_id: String,
  payment_amount: Number,
  payment_currency: String,
  payment_completed_at: Date,
  created_at: Date
}
```

#### rush_status
```javascript
{
  _id: ObjectId,
  date: String, // ISO date string
  time_slot: String,
  status: String, // 'high', 'medium', 'low'
  created_at: Date
}
```

#### counters
```javascript
{
  _id: String, // "order_number"
  sequence_value: Number
}
```

### Indexes Created
- **stationery_items**: `name`, `stock_quantity`
- **bookings**: `order_number` (unique), `customer_name`, `date`, `date + time_slot`, `created_at`
- **rush_status**: `date + time_slot` (unique)

## Authentication

### Default Admin Credentials
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Changing Admin Credentials
To change the admin credentials, update the authentication logic in `src/lib/mongodb.js`:

```javascript
// In the signInWithPassword method
if (email === '<EMAIL>' && password === 'your-new-password') {
  // ... authentication logic
}
```

## Testing the Setup

### Step 1: Start the Application
```bash
npm run dev
```

### Step 2: Test Customer Interface
1. Go to http://localhost:5173/
2. You should see the stationery items loaded
3. Try adding items to cart and making a booking

### Step 3: Test Admin Interface
1. Go to http://localhost:5173/admin/login
2. Login with admin credentials
3. Verify you can see the admin dashboard
4. Check that orders and stock management work

## Troubleshooting

### Connection Issues
- **Local MongoDB**: Ensure MongoDB service is running
- **Atlas**: Check network access and connection string
- **Firewall**: Ensure port 27017 is open for local MongoDB

### Authentication Issues
- Verify admin credentials in the code
- Check JWT secret is set in environment variables
- Clear browser localStorage if needed

### Database Issues
- Run `npm run init-db` again if collections are missing
- Check MongoDB logs for errors
- Verify database permissions

## Migration from Supabase

If you're migrating from Supabase, the application will automatically work with MongoDB once configured. The main differences:

1. **IDs**: MongoDB uses `_id` instead of `id` (automatically handled)
2. **Authentication**: Now uses JWT tokens instead of Supabase Auth
3. **Queries**: Converted from SQL to MongoDB queries
4. **Functions**: PostgreSQL functions replaced with JavaScript logic

## Production Considerations

1. **Security**: Change default admin credentials
2. **JWT Secret**: Use a strong, unique JWT secret
3. **Database**: Use MongoDB Atlas or properly secured MongoDB instance
4. **Indexes**: Monitor query performance and add indexes as needed
5. **Backup**: Set up regular database backups
6. **Monitoring**: Implement database monitoring and alerting

## Need Help?

1. Check MongoDB connection in browser console
2. Verify environment variables are loaded
3. Run database initialization script
4. Check MongoDB server logs
5. Ensure all dependencies are installed: `npm install`
